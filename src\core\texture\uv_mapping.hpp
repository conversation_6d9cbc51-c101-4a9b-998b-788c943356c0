// src/core/texture/uv_mapping.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// UV Mapping Enhancement - Multiple UV sets and coordinate transformation

#pragma once

#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include <cmath>

namespace photon {

/**
 * @brief UV coordinate transformation
 * 
 * Provides comprehensive UV coordinate transformation capabilities including
 * scaling, offset, rotation, and flipping operations.
 */
struct UVTransform {
    Vec2 scale = Vec2(1.0f);        ///< UV scale factors (default: no scaling)
    Vec2 offset = Vec2(0.0f);       ///< UV offset (default: no offset)
    float rotation = 0.0f;          ///< UV rotation in radians (default: no rotation)
    bool flipU = false;             ///< Flip U coordinate (default: no flip)
    bool flipV = false;             ///< Flip V coordinate (default: no flip)
    
    /**
     * @brief Default constructor (identity transform)
     */
    UVTransform() = default;
    
    /**
     * @brief Constructor with parameters
     */
    UVTransform(const Vec2& scale, const Vec2& offset = Vec2(0.0f), 
                float rotation = 0.0f, bool flipU = false, bool flipV = false)
        : scale(scale), offset(offset), rotation(rotation), flipU(flipU), flipV(flipV) {}
    
    /**
     * @brief Apply transformation to UV coordinates
     * 
     * Transformation order: flip -> scale -> rotate -> offset
     * This order ensures predictable and intuitive results.
     * 
     * @param uv Input UV coordinates
     * @return Transformed UV coordinates
     */
    Vec2 transform(const Vec2& uv) const;
    
    /**
     * @brief Apply inverse transformation to UV coordinates
     * 
     * Useful for reverse mapping operations.
     * 
     * @param uv Input UV coordinates
     * @return Inverse transformed UV coordinates
     */
    Vec2 inverseTransform(const Vec2& uv) const;
    
    /**
     * @brief Check if this is an identity transform
     */
    bool isIdentity() const;
    
    /**
     * @brief Combine with another transform
     */
    UVTransform combine(const UVTransform& other) const;
    
    // Static factory methods for common transforms
    
    /**
     * @brief Create identity transform
     */
    static UVTransform identity() { return UVTransform(); }
    
    /**
     * @brief Create scale transform
     */
    static UVTransform createScale(float scaleU, float scaleV) {
        return UVTransform(Vec2(scaleU, scaleV));
    }
    
    /**
     * @brief Create uniform scale transform
     */
    static UVTransform createScale(float scale) {
        return UVTransform(Vec2(scale));
    }
    
    /**
     * @brief Create offset transform
     */
    static UVTransform createOffset(float offsetU, float offsetV) {
        UVTransform t;
        t.offset = Vec2(offsetU, offsetV);
        return t;
    }
    
    /**
     * @brief Create rotation transform
     */
    static UVTransform createRotation(float angleRadians) {
        UVTransform t;
        t.rotation = angleRadians;
        return t;
    }
    
    /**
     * @brief Create flip transform
     */
    static UVTransform createFlip(bool flipU, bool flipV) {
        UVTransform t;
        t.flipU = flipU;
        t.flipV = flipV;
        return t;
    }
};

/**
 * @brief UV mapping modes for procedural coordinate generation
 */
enum class UVMappingMode {
    VERTEX_UV,      ///< Use vertex UV coordinates (default)
    PLANAR_XY,      ///< Planar mapping on XY plane (Z-axis projection)
    PLANAR_XZ,      ///< Planar mapping on XZ plane (Y-axis projection)
    PLANAR_YZ,      ///< Planar mapping on YZ plane (X-axis projection)
    CYLINDRICAL_Y,  ///< Cylindrical mapping around Y axis
    CYLINDRICAL_X,  ///< Cylindrical mapping around X axis
    CYLINDRICAL_Z,  ///< Cylindrical mapping around Z axis
    SPHERICAL,      ///< Spherical mapping
    CUBIC,          ///< Cubic mapping (box projection)
    TRIPLANAR       ///< Triplanar mapping (blend of 3 planar projections)
};

/**
 * @brief UV mapping generator for procedural coordinate generation
 * 
 * Provides comprehensive UV coordinate generation from 3D positions
 * using various projection methods.
 */
class UVMapping {
public:
    /**
     * @brief Constructor
     * @param mode UV mapping mode
     * @param transform UV transformation to apply
     */
    UVMapping(UVMappingMode mode = UVMappingMode::VERTEX_UV, 
              const UVTransform& transform = UVTransform::identity());
    
    /**
     * @brief Generate UV coordinates from 3D position
     * @param position 3D world position
     * @param normal Surface normal (optional, used for some mapping modes)
     * @return Generated UV coordinates
     */
    Vec2 generateUV(const Vec3& position, const Vec3& normal = Vec3(0, 1, 0)) const;
    
    /**
     * @brief Transform existing UV coordinates
     * @param uv Input UV coordinates
     * @return Transformed UV coordinates
     */
    Vec2 transformUV(const Vec2& uv) const;
    
    // Getters and setters
    
    /**
     * @brief Set mapping mode
     */
    void setMappingMode(UVMappingMode mode) { m_mode = mode; }
    
    /**
     * @brief Set UV transform
     */
    void setTransform(const UVTransform& transform) { m_transform = transform; }
    
    /**
     * @brief Set mapping scale (convenience method)
     */
    void setScale(float scaleU, float scaleV);
    
    /**
     * @brief Set mapping offset (convenience method)
     */
    void setOffset(float offsetU, float offsetV);
    
    /**
     * @brief Set mapping rotation (convenience method)
     */
    void setRotation(float angleRadians);
    
    /**
     * @brief Get mapping mode
     */
    UVMappingMode getMappingMode() const { return m_mode; }
    
    /**
     * @brief Get UV transform
     */
    const UVTransform& getTransform() const { return m_transform; }
    
    /**
     * @brief Get mapping mode name (for debugging)
     */
    const char* getMappingModeName() const;

private:
    UVMappingMode m_mode;
    UVTransform m_transform;
    
    // Internal mapping generation methods
    
    /**
     * @brief Generate planar UV coordinates
     * @param position 3D position
     * @param axis Primary axis (0=X, 1=Y, 2=Z)
     * @return Generated UV coordinates
     */
    Vec2 generatePlanarUV(const Vec3& position, int axis) const;
    
    /**
     * @brief Generate cylindrical UV coordinates
     * @param position 3D position
     * @param axis Cylinder axis (0=X, 1=Y, 2=Z)
     * @return Generated UV coordinates
     */
    Vec2 generateCylindricalUV(const Vec3& position, int axis) const;
    
    /**
     * @brief Generate spherical UV coordinates
     * @param position 3D position
     * @return Generated UV coordinates
     */
    Vec2 generateSphericalUV(const Vec3& position) const;
    
    /**
     * @brief Generate cubic UV coordinates
     * @param position 3D position
     * @param normal Surface normal
     * @return Generated UV coordinates
     */
    Vec2 generateCubicUV(const Vec3& position, const Vec3& normal) const;
    
    /**
     * @brief Generate triplanar UV coordinates
     * @param position 3D position
     * @param normal Surface normal
     * @return Generated UV coordinates
     */
    Vec2 generateTriplanarUV(const Vec3& position, const Vec3& normal) const;
};

} // namespace photon
